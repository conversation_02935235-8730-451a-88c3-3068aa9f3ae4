name: GitHub CI

on:
  pull_request:
  push:
  workflow_dispatch:
  schedule:
    - cron: 0 0 * * 0

defaults:
  run:
    shell: 'bash -Eeuo pipefail -x {0}'

concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true

jobs:

  generate-jobs:
    name: Generate Jobs
    runs-on: ubuntu-latest
    outputs:
      strategy: ${{ steps.generate-jobs.outputs.strategy }}
    steps:
      - uses: actions/checkout@v4
      - uses: docker-library/bashbrew@HEAD
      - id: generate-jobs
        name: Generate Jobs
        run: |
          strategy="$("$BASHBREW_SCRIPTS/github-actions/generate.sh")"

          EOF="EOF-$RANDOM-$RANDOM-$RANDOM"
          echo "strategy<<$EOF" >> "$GITHUB_OUTPUT"
          jq <<<"$strategy" . | tee -a "$GITHUB_OUTPUT"
          echo "$EOF" >> "$GITHUB_OUTPUT"

  test:
    needs: generate-jobs
    strategy: ${{ fromJson(needs.generate-jobs.outputs.strategy) }}
    name: ${{ matrix.name }}
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - name: Prepare Environment
        run: ${{ matrix.runs.prepare }}
      - name: <PERSON>ull Dependencies
        run: ${{ matrix.runs.pull }}
      - name: Build ${{ matrix.name }}
        run: ${{ matrix.runs.build }}
      - name: History ${{ matrix.name }}
        run: ${{ matrix.runs.history }}
      - name: Test ${{ matrix.name }}
        run: ${{ matrix.runs.test }}
      - name: '"docker images"'
        run: ${{ matrix.runs.images }}
