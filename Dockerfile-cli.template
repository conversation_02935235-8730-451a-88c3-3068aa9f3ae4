{{ include "shared" -}}
FROM alpine:3.21

RUN apk add --no-cache \
		ca-certificates \
# DOCKER_HOST=ssh://... -- https://github.com/docker/cli/pull/1014
		openssh-client \
# https://github.com/docker-library/docker/issues/482#issuecomment-2197116408
		git

# ensure that nsswitch.conf is set up for Go's "netgo" implementation (which <PERSON><PERSON> explicitly uses)
# - https://github.com/moby/moby/blob/v24.0.6/hack/make.sh#L111
# - https://github.com/golang/go/blob/go1.19.13/src/net/conf.go#L227-L303
# - docker run --rm debian:stretch grep '^hosts:' /etc/nsswitch.conf
RUN [ -e /etc/nsswitch.conf ] && grep '^hosts: files dns' /etc/nsswitch.conf

# pre-add a "docker" group for socket usage
RUN set -eux; \
	addgroup -g 2375 -S docker

ENV DOCKER_VERSION {{ .version }}

RUN set -eux; \
	\
	{{
		download({
			arches: .arches,
			urlKey: "dockerUrl",
			# TODO sha256Key (once Docker publishes them 😭)
			target: "docker.tgz",
		})
	}}; \
	\
	tar --extract \
		--file docker.tgz \
		--strip-components 1 \
		--directory /usr/local/bin/ \
		--no-same-owner \
		'docker/docker' \
	; \
	rm docker.tgz; \
	\
	docker --version
{{
	{
		buildx: .buildx,
		compose: .compose,
	}
	| to_entries | map(
		.key as $key | .value | (
-}}

ENV DOCKER_{{ $key | ascii_upcase }}_VERSION {{ .version }}
RUN set -eux; \
	\
	{{
		download({
			arches: .arches,
			urlKey: "url",
			sha256Key: "sha256",
			target: ("docker-" + $key),
			missingArchWarning: true,
		})
	}}; \
	\
	plugin='/usr/local/libexec/docker/cli-plugins/docker-{{ $key }}'; \
	mkdir -p "$(dirname "$plugin")"; \
	mv -vT {{ "docker-" + $key | @sh }} "$plugin"; \
	chmod +x "$plugin"; \
	\
{{ if $key == "compose" then ( -}}
	ln -sv "$plugin" /usr/local/bin/; \
	docker-{{ $key }} --version; \
{{ ) else "" end -}}
	docker {{ $key }} version
{{
		)
	)
	| add
-}}

COPY modprobe.sh /usr/local/bin/modprobe
COPY docker-entrypoint.sh /usr/local/bin/

# https://github.com/docker-library/docker/pull/166
#   dockerd-entrypoint.sh uses DOCKER_TLS_CERTDIR for auto-generating TLS certificates
#   docker-entrypoint.sh uses DOCKER_TLS_CERTDIR for auto-setting DOCKER_TLS_VERIFY and DOCKER_CERT_PATH
# (For this to work, at least the "client" subdirectory of this path needs to be shared between the client and server containers via a volume, "docker cp", or other means of data sharing.)
ENV DOCKER_TLS_CERTDIR=/certs
# also, ensure the directory pre-exists and has wide enough permissions for "dockerd-entrypoint.sh" to create subdirectories, even when run in "rootless" mode
RUN mkdir /certs /certs/client && chmod 1777 /certs /certs/client
# (doing both /certs and /certs/client so that if Docker does a "copy-up" into a volume defined on /certs/client, it will "do the right thing" by default in a way that still works for rootless users)

ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["sh"]
