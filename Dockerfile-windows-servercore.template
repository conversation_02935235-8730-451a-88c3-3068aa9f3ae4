FROM mcr.microsoft.com/windows/{{ env.windowsVariant }}:{{ env.windowsRelease }}

# $ProgressPreference: https://github.com/PowerShell/PowerShell/issues/2138#issuecomment-251261324
SHELL ["powershell", "-Command", "$ErrorActionPreference = 'Stop'; $ProgressPreference = 'SilentlyContinue';"]

# PATH isn't actually set in the Docker image, so we have to set it from within the container
RUN $newPath = ('{0}\docker;{1}' -f $env:ProgramFiles, $env:PATH); \
	Write-Host ('Updating PATH: {0}' -f $newPath); \
	[Environment]::SetEnvironmentVariable('PATH', $newPath, [EnvironmentVariableTarget]::Machine);
# doing this first to share cache across versions more aggressively

ENV DOCKER_VERSION {{ .version }}
ENV DOCKER_URL {{ .arches["windows-amd64"].dockerUrl }}
# TODO ENV DOCKER_SHA256
# https://github.com/docker/docker-ce/blob/5b073ee2cf564edee5adca05eee574142f7627bb/components/packaging/static/hash_files !!
# (no SHA file artifacts on download.docker.com yet as of 2017-06-07 though)

RUN Write-Host ('Downloading {0} ...' -f $env:DOCKER_URL); \
	Invoke-WebRequest -Uri $env:DOCKER_URL -OutFile 'docker.zip'; \
	\
	Write-Host 'Expanding ...'; \
	Expand-Archive docker.zip -DestinationPath $env:ProgramFiles; \
# (this archive has a "docker/..." directory in it already)
	\
	Write-Host 'Removing ...'; \
	Remove-Item @( \
			'docker.zip', \
			('{0}\docker\dockerd.exe' -f $env:ProgramFiles) \
		) -Force; \
	\
	Write-Host 'Verifying install ("docker --version") ...'; \
	docker --version; \
	\
	Write-Host 'Complete.';

# https://github.com/docker-library/docker/issues/409#issuecomment-1462868414
{{
	{
		buildx: .buildx,
		compose: .compose,
	}
	| to_entries | map(
		.key as $key | ($key | ascii_upcase) as $KEY | .value | (
-}}
ENV DOCKER_{{ $KEY }}_VERSION {{ .version }}
ENV DOCKER_{{ $KEY }}_URL {{ .arches["windows-amd64"].url }}
ENV DOCKER_{{ $KEY }}_SHA256 {{ .arches["windows-amd64"].sha256 }}
RUN $dir = ('{0}\docker\cli-plugins' -f $env:ProgramFiles); \
	Write-Host ('Creating {0} ...' -f $dir); \
	New-Item -ItemType Directory $dir -Force; \
	\
	$plugin = ('{0}\docker-{{ $key }}.exe' -f $dir); \
	Write-Host ('Downloading {0} ...' -f $env:DOCKER_{{ $KEY }}_URL); \
	Invoke-WebRequest -Uri $env:DOCKER_{{ $KEY }}_URL -OutFile $plugin; \
	\
	Write-Host ('Verifying sha256 ({0}) ...' -f $env:DOCKER_{{ $KEY }}_SHA256); \
	if ((Get-FileHash $plugin -Algorithm sha256).Hash -ne $env:DOCKER_{{ $KEY }}_SHA256) { \
		Write-Host 'FAILED!'; \
		exit 1; \
	}; \
	\
	Write-Host 'Verifying install ("docker {{ $key }} version") ...'; \
	docker {{ $key }} version; \
{{ if $key == "compose" then ( -}}
	\
	$link = ('{0}\docker\docker-{{ $key }}.exe' -f $env:ProgramFiles); \
	Write-Host ('Linking {0} to {1} ...' -f $plugin, $link); \
	New-Item -ItemType SymbolicLink -Path $link -Target $plugin; \
	\
	Write-Host 'Verifying install ("docker-{{ $key }} --version") ...'; \
	docker-{{ $key }} --version; \
{{ ) else "" end -}}
	\
	Write-Host 'Complete.';
{{
		)
	)
	| add
-}}
