# https://github.com/docker-library/docker

## Maintained by: [<PERSON><PERSON><PERSON> (of the Docker Project)](https://github.com/docker-library/docker)

This is the Git repo of the [Docker "Official Image"](https://github.com/docker-library/official-images#what-are-official-images) for [`docker`](https://hub.docker.com/_/docker/). See [the Docker Hub page](https://hub.docker.com/_/docker/) for the full readme on how to use this Docker image and for information regarding contributing and issues.

The [full image description on Docker Hub](https://hub.docker.com/_/docker/) is generated/maintained over in [the docker-library/docs repository](https://github.com/docker-library/docs), specifically in [the `docker` directory](https://github.com/docker-library/docs/tree/master/docker).

## See a change merged here that doesn't show up on Docker Hub yet?

For more information about the full official images change lifecycle, see [the "An image's source changed in Git, now what?" FAQ entry](https://github.com/docker-library/faq#an-images-source-changed-in-git-now-what).

For outstanding `docker` image PRs, check [PRs with the "library/docker" label on the official-images repository](https://github.com/docker-library/official-images/labels/library%2Fdocker). For the current "source of truth" for [`docker`](https://hub.docker.com/_/docker/), see [the `library/docker` file in the official-images repository](https://github.com/docker-library/official-images/blob/master/library/docker).

## Custom Dind Image

### Build
```bash
docker build -t custom-dind:latest -f custom-dind/Dockerfile .
```

### Usage
```bash
# 启动容器
 docker run -d \
  --name some-docker \
  --privileged \
  -v /sys/fs/cgroup:/sys/fs/cgroup:ro \
  -p 2375:2375 \
  custom-dind:latest

# 执行命令
docker exec -it some-docker docker info
```

### Features
- 基于Ubuntu 22.04 LTS
- 完整Docker CLI、Buildx和Compose支持
- 使用Supervisor管理多服务
- 支持非特权模式运行
- x86_64架构优化
- 多阶段构建优化镜像大小

<!-- THIS FILE IS GENERATED BY https://github.com/docker-library/docs/blob/master/generate-repo-stub-readme.sh -->
