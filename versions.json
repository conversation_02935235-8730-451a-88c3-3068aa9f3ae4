{"28": {"arches": {"amd64": {"dockerUrl": "https://download.docker.com/linux/static/stable/x86_64/docker-28.2.1.tgz", "rootlessExtrasUrl": "https://download.docker.com/linux/static/stable/x86_64/docker-rootless-extras-28.2.1.tgz"}, "arm32v6": {"dockerUrl": "https://download.docker.com/linux/static/stable/armel/docker-28.2.1.tgz"}, "arm32v7": {"dockerUrl": "https://download.docker.com/linux/static/stable/armhf/docker-28.2.1.tgz"}, "arm64v8": {"dockerUrl": "https://download.docker.com/linux/static/stable/aarch64/docker-28.2.1.tgz", "rootlessExtrasUrl": "https://download.docker.com/linux/static/stable/aarch64/docker-rootless-extras-28.2.1.tgz"}, "windows-amd64": {"dockerUrl": "https://download.docker.com/win/static/stable/x86_64/docker-28.2.1.zip"}}, "buildx": {"arches": {"amd64": {"file": "buildx-v0.24.0.linux-amd64", "sha256": "c41ed17ec05b6ebb50eeb02fb26cce90f16cd260b8d26ce73963428c6b2d6508", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.linux-amd64"}, "arm32v6": {"file": "buildx-v0.24.0.linux-arm-v6", "sha256": "591abb51afe942814a45f4f3d0f1a97fe8c5c212142bded66025ae019136bac8", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.linux-arm-v6"}, "arm32v7": {"file": "buildx-v0.24.0.linux-arm-v7", "sha256": "69a3afa3d22867ea67b87e5f205574478e7a795599c471b61575bacf455452ae", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.linux-arm-v7"}, "arm64v8": {"file": "buildx-v0.24.0.linux-arm64", "sha256": "ad33819d085a635e3b4400a412bd2b4e943bfbc830366d78f50579bae48f8053", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.linux-arm64"}, "freebsd-amd64": {"file": "buildx-v0.24.0.freebsd-amd64", "sha256": "889224cf7885ab6b1890c5019fe798021827ac73cfa6c8f1931418e09a4d8a18", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.freebsd-amd64"}, "freebsd-arm64v8": {"file": "buildx-v0.24.0.freebsd-arm64", "sha256": "2850060b31468f928cd34a25aa65725944f8969e4d09715499333f063a2f9bca", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.freebsd-arm64"}, "netbsd-amd64": {"file": "buildx-v0.24.0.netbsd-amd64", "sha256": "2e83d94835a1a19ff90c5a83e4039b445f8e3e1fc13d447a643d87cbbf2645aa", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.netbsd-amd64"}, "netbsd-arm64v8": {"file": "buildx-v0.24.0.netbsd-arm64", "sha256": "53964d5bfa7b4ed5977f0980f41a365f05808e98c674ce245692409c03897381", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.netbsd-arm64"}, "openbsd-amd64": {"file": "buildx-v0.24.0.openbsd-amd64", "sha256": "995c03911b43075a84c6310a0cf40d35f3752787e3bec8bb763f048d2036385c", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.openbsd-amd64"}, "openbsd-arm64v8": {"file": "buildx-v0.24.0.openbsd-arm64", "sha256": "9647a7d528058673ba26acf7d146e253fe2f6029c9a21e73b660659d6e4918b8", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.openbsd-arm64"}, "ppc64le": {"file": "buildx-v0.24.0.linux-ppc64le", "sha256": "90c02625d1e52abd8e6089854208963651ea727028aaca58b29847dc594c01f8", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.linux-ppc64le"}, "riscv64": {"file": "buildx-v0.24.0.linux-riscv64", "sha256": "3031cf533e015ea77425446e4bc87173f1316447ed3369c2898f73ac353de404", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.linux-riscv64"}, "s390x": {"file": "buildx-v0.24.0.linux-s390x", "sha256": "821ea62254a7be6cab51c5ebefc9ba74b7e2dd902c78d16b268850dc29869ca2", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.linux-s390x"}, "windows-amd64": {"file": "buildx-v0.24.0.windows-amd64.exe", "sha256": "8dec102c8eb14f434707cc05a8f0e366c090ded6ad74d9c5f8a64a9c0b766140", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.windows-amd64.exe"}, "windows-arm64v8": {"file": "buildx-v0.24.0.windows-arm64.exe", "sha256": "fc0cd1fa1594ad6e0fc907a6f00b618ede1a3958ccc4ddf324dea3500946e046", "url": "https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.windows-arm64.exe"}}, "version": "0.24.0"}, "compose": {"arches": {"amd64": {"file": "docker-compose-linux-x86_64", "sha256": "9040bd35b2cc0783ce6c5de491de7e52e24d4137dbfc5de8a524f718fc23556c", "url": "https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-linux-x86_64"}, "arm32v6": {"file": "docker-compose-linux-armv6", "sha256": "8260c11228337291dd2adcc1ee957756581047c5f40ad5ff6917660e8ebe7e61", "url": "https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-linux-armv6"}, "arm32v7": {"file": "docker-compose-linux-armv7", "sha256": "9e9d20ebc4a094ee7788fbb5bddf70b0b319a55eee134db195d1e47f078ae0dc", "url": "https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-linux-armv7"}, "arm64v8": {"file": "docker-compose-linux-aarch64", "sha256": "d1148609319706a57b755ff0f61d604a63a8cf57adb24c17535baa766ff14b4f", "url": "https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-linux-aarch64"}, "darwin-amd64": {"file": "docker-compose-dar<PERSON>-x86_64", "sha256": "e4479c44049f02e85b45cda129fd4edb1f389ebee894a1dc15a153898b87a160", "url": "https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-darwin-x86_64"}, "darwin-arm64v8": {"file": "docker-compose-darwin-aarch64", "sha256": "065c65ec0dd579a82854fff9a8841f5be139362371d3cd5cf26cfdc953dbbbf3", "url": "https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-darwin-aarch64"}, "ppc64le": {"file": "docker-compose-linux-ppc64le", "sha256": "14b5db45d45808ece42066e4c978a6dddeb0c7ceffd656abfcb8182515fb9c7c", "url": "https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-linux-ppc64le"}, "riscv64": {"file": "docker-compose-linux-riscv64", "sha256": "17b86e88985f7ac6f282ea36e585d15a586584bc4f853466f92a9aed031772ed", "url": "https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-linux-riscv64"}, "s390x": {"file": "docker-compose-linux-s390x", "sha256": "65fe31a89326fb6de9f0e0c93c9abb0e88e407febc16b3551b92507e1ffbc965", "url": "https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-linux-s390x"}, "windows-amd64": {"file": "docker-compose-windows-x86_64.exe", "sha256": "82ebec0022949087f883b3dffa0d7e57a2a141203ad31c012381d2754962c905", "url": "https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-windows-x86_64.exe"}, "windows-arm64v8": {"file": "docker-compose-windows-aarch64.exe", "sha256": "c838e1f14dcb50b11498cc7e00b2faeb7816f422776d2800a62e47e039f56dad", "url": "https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-windows-aarch64.exe"}}, "version": "2.36.2"}, "dindCommit": "8d9e3502aba39127e4d12196dae16d306f76993d", "variants": ["cli", "dind", "dind-rootless", "windows/windowsservercore-ltsc2025", "windows/windowsservercore-ltsc2022", "windows/windowsservercore-1809"], "version": "28.2.1"}, "28-rc": null}